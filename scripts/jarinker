#!/bin/bash

# Jarinker Remote Execution Script
# This script automatically downloads and executes jarinker without requiring manual installation
# Usage: curl -sSL https://raw.githubusercontent.com/DanielLiu1123/jarinker/main/scripts/jarinker | bash -s -- [jarinker-args]

set -euo pipefail

# Global configuration
readonly SCRIPT_NAME="jarinker"
readonly GITHUB_REPO="DanielLiu1123/jarinker"
readonly REQUIRED_JAVA_VERSION=17
readonly DEFAULT_CACHE_DIR="$HOME/.jarinker"

# User configurable variables
JARINKER_CACHE_DIR="${JARINKER_CACHE_DIR:-$DEFAULT_CACHE_DIR}"
JARINKER_VERSION="${JARINKER_VERSION:-v0.1.0-RC1}"
JARINKER_OFFLINE="${JARINKER_OFFLINE:-false}"
JARINKER_VERBOSE="${JARINKER_VERBOSE:-false}"

# Colors for output
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m' # No Color

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1" >&2
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1" >&2
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1" >&2
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1" >&2
}

# Show help information
show_help() {
    cat << 'EOF'
Jarinker Remote Execution Script

USAGE:
    # Install to PATH (recommended)
    curl -sSL https://raw.githubusercontent.com/DanielLiu1123/jarinker/main/scripts/jarinker | bash

    # Then use directly
    jarinker analyze -cp libs/ build/classes/java/main

    # Or execute directly with arguments
    curl -sSL https://raw.githubusercontent.com/DanielLiu1123/jarinker/main/scripts/jarinker | bash -s -- analyze -cp libs/ build/

    # Or download and use
    curl -sSL https://raw.githubusercontent.com/DanielLiu1123/jarinker/main/scripts/jarinker -o jarinker && chmod +x jarinker
    ./jarinker analyze -cp libs/ build/

ENVIRONMENT VARIABLES:
    JARINKER_VERSION     Specify jarinker version (default: latest)
    JARINKER_CACHE_DIR   Cache directory (default: ~/.jarinker)
    JARINKER_OFFLINE     Use offline mode, only use cached versions (default: false)
    JARINKER_VERBOSE     Enable verbose output (default: false)

SCRIPT OPTIONS:
    --script-help        Show this help message
    --script-version     Show script version
    --script-clean       Clean cache directory
    --script-update      Force update to latest version
    --script-install     Install jarinker to PATH

EXAMPLES:
    # Install to PATH
    curl -sSL https://raw.githubusercontent.com/DanielLiu1123/jarinker/main/scripts/jarinker | bash

    # Use after installation
    jarinker analyze -cp libs/ build/classes/java/main
    jarinker shrink -cp libs/ -o shrunk-libs/ build/classes/java/main

    # Direct execution
    ./jarinker analyze -cp libs/ build/classes/java/main

    # Use specific version
    JARINKER_VERSION=0.1.0-RC1 ./jarinker analyze -cp libs/ build/

    # Verbose mode
    JARINKER_VERBOSE=true ./jarinker --help

For jarinker-specific help, run: jarinker --help (after installation)
EOF
}

# Check if required dependencies are available
check_dependencies() {
    log_info "Checking dependencies..."
    
    # Check Java
    if ! command -v java &> /dev/null; then
        log_error "Java not found. Please install Java $REQUIRED_JAVA_VERSION or later."
        exit 1
    fi
    
    # Check Java version
    local java_version
    java_version=$(java -version 2>&1 | head -n1 | cut -d'"' -f2 | cut -d'.' -f1)
    if [[ "$java_version" -lt "$REQUIRED_JAVA_VERSION" ]]; then
        log_error "Java $REQUIRED_JAVA_VERSION or later is required. Found Java $java_version."
        exit 1
    fi
    
    log_success "Java $java_version found"
    
    # Check required tools
    local missing_tools=()
    for tool in curl unzip; do
        if ! command -v "$tool" &> /dev/null; then
            missing_tools+=("$tool")
        fi
    done
    
    if [[ ${#missing_tools[@]} -gt 0 ]]; then
        log_error "Missing required tools: ${missing_tools[*]}"
        log_error "Please install the missing tools and try again."
        exit 1
    fi
}

# Get the latest version from GitHub API
get_latest_version() {
    log_info "Fetching latest version from GitHub..."
    
    local api_url="https://api.github.com/repos/$GITHUB_REPO/releases/latest"
    local response

    if ! response=$(curl -s --fail "$api_url" 2>/dev/null); then
        log_error "Failed to fetch latest version from GitHub API"
        log_error "$response"
        exit 1
    fi
    
    local version
    version=$(echo "$response" | grep '"tag_name":' | sed -E 's/.*"([^"]+)".*/\1/' | head -n1)
    
    if [[ -z "$version" ]]; then
        log_error "Failed to parse version from GitHub API response"
        exit 1
    fi
    
    echo "$version"
}

# Download and extract jarinker
download_jarinker() {
    local version="$1"
    local download_url="$2"
    local cache_dir="$JARINKER_CACHE_DIR/versions/$version"
    local zip_file="$cache_dir/jarinker-cli-$version.zip"

    local download_cmd="curl -L \"$download_url\" -o \"$zip_file\""
    log_info "Downloading jarinker $version: $download_cmd"
    
    # Create cache directory
    mkdir -p "$cache_dir"

    eval "$download_cmd"

    if [[ ! -f "$zip_file" ]]; then
        log_error "Download failed: $zip_file not found"
        exit 1
    fi

    log_success "Downloaded jarinker $version to $zip_file"
    
    log_info "Extracting jarinker..."
    
    # Extract to cache directory
    if ! unzip -q "$zip_file" -d "$cache_dir"; then
        log_error "Failed to extract $zip_file"
        rm -f "$zip_file"
        exit 1
    fi
    
    # Find the jarinker executable
    local jarinker_bin
    jarinker_bin=$(find "$cache_dir" -name "jarinker" -type f -executable | head -n1)
    
    if [[ -z "$jarinker_bin" ]]; then
        log_error "jarinker executable not found in extracted files"
        exit 1
    fi
    
    # Make sure it's executable
    chmod +x "$jarinker_bin"
    
    log_success "jarinker $version downloaded and extracted"
    echo "$jarinker_bin"
}

# Get or download jarinker
ensure_jarinker_available() {
    local version="$1"
    local cache_dir="$JARINKER_CACHE_DIR/versions/$version"
    
    # Look for existing installation
    local jarinker_bin
    jarinker_bin=$(find "$cache_dir" -name "jarinker" -type f 2>/dev/null | head -n1)
    
    if [[ -n "$jarinker_bin" && -x "$jarinker_bin" ]]; then
        log_success "Using cached jarinker $version"
        echo "$jarinker_bin"
        return
    fi
    
    # Download jarinker
    local download_url
    download_url="https://github.com/$GITHUB_REPO/releases/download/untagged-9ed8f7f7efe17269a084/jarinker-cli-$version.zip"
    download_jarinker "$version" "$download_url"
}

# Execute jarinker with provided arguments
execute_jarinker() {
    local jarinker_bin="$1"
    shift
    
    log_info "Executing: $jarinker_bin $*"
    
    # Execute jarinker with all provided arguments
    exec "$jarinker_bin" "$@"
}

# Clean cache directory
clean_cache() {
    if [[ -d "$JARINKER_CACHE_DIR" ]]; then
        log_info "Cleaning cache directory: $JARINKER_CACHE_DIR"
        rm -rf "$JARINKER_CACHE_DIR"
        log_success "Cache cleaned"
    else
        log_info "Cache directory does not exist: $JARINKER_CACHE_DIR"
    fi
}

# Parse script-specific arguments
parse_script_args() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            --script-help)
                show_help
                exit 0
                ;;
            --script-version)
                echo "jarinker remote execution script v1.0.0"
                exit 0
                ;;
            --script-clean)
                clean_cache
                exit 0
                ;;
            *)
                # Not a script argument, pass through to jarinker
                break
                ;;
        esac
        shift
    done

    # Return remaining arguments
    echo "$@"
}

# Install jarinker to PATH
install_to_path() {
    local install_dir="$HOME/.local/bin"
    local script_path="$install_dir/jarinker"

    log_info "Installing jarinker to PATH..."

    # Create install directory if it doesn't exist
    if [[ ! -d "$install_dir" ]]; then
        log_info "Creating directory: $install_dir"
        if ! mkdir -p "$install_dir"; then
            log_error "Failed to create directory: $install_dir"
            log_error "Please check permissions or run with appropriate privileges"
            exit 1
        fi
    fi

    # Create the PATH script
    cat > "$script_path" << 'EOF'
#!/bin/bash
# Jarinker PATH wrapper script
# This script calls the cached jarinker installation

JARINKER_CACHE_DIR="${JARINKER_CACHE_DIR:-$HOME/.jarinker}"

# Find the latest cached jarinker
find_jarinker() {
    local cache_dir="$JARINKER_CACHE_DIR"
    if [[ ! -d "$cache_dir" ]]; then
        echo "Error: jarinker not found. Please run the installation script first:" >&2
        echo "curl -sSL https://raw.githubusercontent.com/DanielLiu1123/jarinker/main/scripts/jarinker | bash" >&2
        exit 1
    fi

    # Look for jarinker executable in cache
    local jarinker_bin
    jarinker_bin=$(find "$cache_dir" -name "jarinker" -type f 2>/dev/null | head -n1)

    if [[ -z "$jarinker_bin" ]]; then
        echo "Error: jarinker executable not found in cache. Please run:" >&2
        echo "curl -sSL https://raw.githubusercontent.com/DanielLiu1123/jarinker/main/scripts/jarinker | bash" >&2
        exit 1
    fi

    echo "$jarinker_bin"
}

# Execute jarinker
jarinker_bin=$(find_jarinker)
exec "$jarinker_bin" "$@"
EOF

    # Make it executable
    if ! chmod +x "$script_path"; then
        log_error "Failed to make script executable: $script_path"
        exit 1
    fi

    log_success "jarinker installed to: $script_path"

    # Check if install directory is in PATH
    if [[ ":$PATH:" != *":$install_dir:"* ]]; then
        log_warn "Installation directory is not in your PATH: $install_dir"
        log_warn "Add the following line to your shell profile (~/.bashrc, ~/.zshrc, etc.):"
        echo ""
        echo "    export PATH=\"$install_dir:\$PATH\""
        echo ""
        log_warn "Or run jarinker with full path: $script_path"
    else
        log_success "Installation directory is in your PATH"
        echo ""
        echo "You can now use jarinker directly:"
        echo "  jarinker --help"
        echo "  jarinker analyze -cp libs/ build/classes/java/main"
        echo "  jarinker shrink -cp libs/ -o shrunk-libs/ build/classes/java/main"
    fi
}

# Main function
main() {
    # Check dependencies first
    check_dependencies

    # Determine version to use
    local version="$JARINKER_VERSION"

    # Always ensure jarinker is available first
    local jarinker_bin
    jarinker_bin=$(ensure_jarinker_available "$version")

    # If no arguments provided, install to PATH after downloading
    if [[ $# -eq 0 ]]; then
        log_info "No arguments provided. Installing jarinker to PATH..."
        install_to_path
        exit 0
    fi

    # Parse script-specific arguments
    local remaining_args
    remaining_args=$(parse_script_args "$@")

    # Execute jarinker with remaining arguments
    execute_jarinker "$jarinker_bin" $remaining_args
}

# Run main function if script is executed directly
if [[ "${BASH_SOURCE[0]}" == "${0}" ]]; then
    main "$@"
fi
